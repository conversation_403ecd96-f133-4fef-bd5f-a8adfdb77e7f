import jsPDF from 'jspdf';
import 'jspdf-autotable';
import * as XLSX from 'xlsx';
import { formatFileSize, calculateStorageStats } from '@/data/landlord/documents/data';

// Helper function to format date
const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

// Export Document List to PDF
export const exportDocumentsToPDF = (documents, filters = {}) => {
  try {
    if (!documents || !Array.isArray(documents) || documents.length === 0) {
      throw new Error('No document data available for export');
    }

    const doc = new jsPDF();
    const currentDate = new Date().toLocaleDateString();
    const allDocuments = documents.flatMap(group => group.documentsList || []);
    
    if (allDocuments.length === 0) {
      throw new Error('No documents found in the provided data');
    }

    // Header
    doc.setFontSize(20);
    doc.setTextColor(59, 130, 246); // Blue-600
    doc.text('Document Vault Report', 20, 25);
    
    doc.setFontSize(12);
    doc.setTextColor(0, 0, 0);
    doc.text(`Generated on: ${currentDate}`, 20, 35);
    
    // Add filters information if any
    let yPosition = 45;
    if (filters.categoryFilter && filters.categoryFilter !== "All Categories") {
      doc.text(`Category Filter: ${filters.categoryFilter}`, 20, yPosition);
      yPosition += 10;
    }
    if (filters.propertyFilter && filters.propertyFilter !== "All Properties") {
      doc.text(`Property Filter: ${filters.propertyFilter}`, 20, yPosition);
      yPosition += 10;
    }
    if (filters.search) {
      doc.text(`Search: ${filters.search}`, 20, yPosition);
      yPosition += 10;
    }
    
    yPosition += 10;

    // Summary Statistics
    doc.setFontSize(16);
    doc.setTextColor(59, 130, 246);
    doc.text('Document Summary', 20, yPosition);
    yPosition += 15;

    // Calculate storage statistics
    const stats = calculateStorageStats(documents);
    const sharedDocuments = allDocuments.filter(doc => doc.isShared).length;
    const uniqueCategories = new Set(allDocuments.map(doc => doc.category)).size;

    doc.setFontSize(12);
    doc.setTextColor(0, 0, 0);

    // Create summary table
    const summaryData = [
      ['Total Documents', stats.totalFiles.toString()],
      ['Total Storage Used', formatFileSize(stats.totalSize)],
      ['Shared Documents', sharedDocuments.toString()],
      ['Categories', uniqueCategories.toString()],
      ['Average File Size', formatFileSize(stats.averageFileSize)]
    ];

    doc.autoTable({
      startY: yPosition,
      head: [['Metric', 'Value']],
      body: summaryData,
      theme: 'grid',
      headStyles: { fillColor: [59, 130, 246] },
      margin: { left: 20, right: 20 },
      columnStyles: {
        0: { fontStyle: 'bold' },
        1: { halign: 'right' }
      }
    });

    yPosition = doc.lastAutoTable.finalY + 20;

    // Document List
    doc.setFontSize(16);
    doc.setTextColor(59, 130, 246);
    doc.text('Document List', 20, yPosition);
    yPosition += 15;

    // Prepare document data for table
    const documentData = allDocuments.map(document => [
      document.originalName,
      document.category,
      document.propertyName,
      formatFileSize(document.fileSize),
      document.fileType.toUpperCase(),
      formatDate(document.uploadDate),
      document.uploadedBy,
      document.isShared ? 'Yes' : 'No'
    ]);

    doc.autoTable({
      startY: yPosition,
      head: [['Document Name', 'Category', 'Property', 'Size', 'Type', 'Upload Date', 'Uploaded By', 'Shared']],
      body: documentData,
      theme: 'striped',
      headStyles: { fillColor: [59, 130, 246] },
      margin: { left: 20, right: 20 },
      styles: { fontSize: 8 },
      columnStyles: {
        0: { cellWidth: 40 },
        1: { cellWidth: 25 },
        2: { cellWidth: 30 },
        3: { cellWidth: 15 },
        4: { cellWidth: 15 },
        5: { cellWidth: 20 },
        6: { cellWidth: 20 },
        7: { cellWidth: 15 }
      }
    });

    // Footer
    const pageCount = doc.internal.getNumberOfPages();
    for (let i = 1; i <= pageCount; i++) {
      doc.setPage(i);
      doc.setFontSize(10);
      doc.setTextColor(128, 128, 128);
      doc.text(`Page ${i} of ${pageCount}`, doc.internal.pageSize.width - 30, doc.internal.pageSize.height - 10);
      doc.text('Generated by Property Harmony - Document Vault', 20, doc.internal.pageSize.height - 10);
    }

    // Save the PDF
    const filename = `document-vault-${new Date().toISOString().split('T')[0]}.pdf`;
    doc.save(filename);
    
    return filename;
  } catch (error) {
    console.error('PDF Export Error:', error);
    throw new Error(`Failed to export PDF: ${error.message}`);
  }
};

// Export Document List to Excel
export const exportDocumentsToExcel = (documents, filters = {}) => {
  try {
    if (!documents || !Array.isArray(documents) || documents.length === 0) {
      throw new Error('No document data available for export');
    }

    const allDocuments = documents.flatMap(group => group.documentsList || []);
    
    if (allDocuments.length === 0) {
      throw new Error('No documents found in the provided data');
    }

    // Create workbook
    const workbook = XLSX.utils.book_new();

    // Summary Sheet
    const stats = calculateStorageStats(documents);
    const sharedDocuments = allDocuments.filter(doc => doc.isShared).length;
    const uniqueCategories = new Set(allDocuments.map(doc => doc.category)).size;

    const summaryData = [
      ['Document Vault Report Summary'],
      ['Generated on:', new Date().toLocaleDateString()],
      [''],
      ['Document Overview'],
      ['Total Documents', stats.totalFiles],
      ['Total Storage Used (bytes)', stats.totalSize],
      ['Total Storage Used (formatted)', formatFileSize(stats.totalSize)],
      ['Shared Documents', sharedDocuments],
      ['Categories', uniqueCategories],
      ['Average File Size (bytes)', stats.averageFileSize],
      ['Average File Size (formatted)', formatFileSize(stats.averageFileSize)],
      [''],
      ['Filters Applied'],
      ['Category Filter', filters.categoryFilter || 'All Categories'],
      ['Property Filter', filters.propertyFilter || 'All Properties'],
      ['Search Filter', filters.search || 'None']
    ];

    const summarySheet = XLSX.utils.aoa_to_sheet(summaryData);
    XLSX.utils.book_append_sheet(workbook, summarySheet, 'Summary');

    // Documents Sheet
    const documentData = [
      ['Document Name', 'Filename', 'Category', 'Property Name', 'File Type', 'File Size (bytes)', 
       'File Size (formatted)', 'Upload Date', 'Last Modified', 'Uploaded By', 'Description', 'Tags',
       'Expiry Date', 'Version', 'Download Count', 'Is Shared', 'Share Link', 'Share Expiry']
    ];

    allDocuments.forEach(document => {
      documentData.push([
        document.originalName,
        document.filename,
        document.category,
        document.propertyName,
        document.fileType,
        document.fileSize,
        formatFileSize(document.fileSize),
        document.uploadDate,
        document.lastModified,
        document.uploadedBy,
        document.description || '',
        document.tags ? document.tags.join(', ') : '',
        document.expiryDate || '',
        document.version,
        document.downloadCount,
        document.isShared ? 'Yes' : 'No',
        document.shareLink || '',
        document.shareExpiry || ''
      ]);
    });

    const documentSheet = XLSX.utils.aoa_to_sheet(documentData);

    // Auto-size columns
    const colWidths = [
      { wch: 30 }, // Document Name
      { wch: 25 }, // Filename
      { wch: 20 }, // Category
      { wch: 25 }, // Property Name
      { wch: 10 }, // File Type
      { wch: 15 }, // File Size (bytes)
      { wch: 15 }, // File Size (formatted)
      { wch: 12 }, // Upload Date
      { wch: 12 }, // Last Modified
      { wch: 15 }, // Uploaded By
      { wch: 30 }, // Description
      { wch: 20 }, // Tags
      { wch: 12 }, // Expiry Date
      { wch: 8 },  // Version
      { wch: 12 }, // Download Count
      { wch: 10 }, // Is Shared
      { wch: 40 }, // Share Link
      { wch: 12 }  // Share Expiry
    ];
    documentSheet['!cols'] = colWidths;

    XLSX.utils.book_append_sheet(workbook, documentSheet, 'Documents');

    // Category Statistics Sheet
    const categoryStats = stats.categoryStats;
    const categoryData = [
      ['Category Statistics'],
      [''],
      ['Category', 'Document Count', 'Total Size (bytes)', 'Total Size (formatted)', 'Average Size (bytes)', 'Average Size (formatted)']
    ];

    Object.entries(categoryStats).forEach(([category, data]) => {
      const avgSize = data.count > 0 ? data.size / data.count : 0;
      categoryData.push([
        category,
        data.count,
        data.size,
        formatFileSize(data.size),
        Math.round(avgSize),
        formatFileSize(avgSize)
      ]);
    });

    const categorySheet = XLSX.utils.aoa_to_sheet(categoryData);
    XLSX.utils.book_append_sheet(workbook, categorySheet, 'Category Stats');

    // File Type Statistics Sheet
    const typeStats = stats.typeStats;
    const typeData = [
      ['File Type Statistics'],
      [''],
      ['File Type', 'Document Count', 'Total Size (bytes)', 'Total Size (formatted)', 'Average Size (bytes)', 'Average Size (formatted)']
    ];

    Object.entries(typeStats).forEach(([type, data]) => {
      const avgSize = data.count > 0 ? data.size / data.count : 0;
      typeData.push([
        type.toUpperCase(),
        data.count,
        data.size,
        formatFileSize(data.size),
        Math.round(avgSize),
        formatFileSize(avgSize)
      ]);
    });

    const typeSheet = XLSX.utils.aoa_to_sheet(typeData);
    XLSX.utils.book_append_sheet(workbook, typeSheet, 'File Type Stats');

    // Save the Excel file
    const filename = `document-vault-${new Date().toISOString().split('T')[0]}.xlsx`;
    XLSX.writeFile(workbook, filename);
    
    return filename;
  } catch (error) {
    console.error('Excel Export Error:', error);
    throw new Error(`Failed to export Excel: ${error.message}`);
  }
};

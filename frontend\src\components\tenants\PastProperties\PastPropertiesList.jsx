import React from 'react';
import PastPropertiesCard from './PastPropertiesCard';

const PastPropertiesList = ({ properties, onViewDocuments }) => {
  if (!properties || properties.length === 0) {
    return (
      <div className="bg-white rounded-lg border border-gray-200 p-8 text-center">
        <div className="text-gray-400 mb-4">
          <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
          </svg>
        </div>
        <h3 className="text-lg font-semibold text-gray-900 mb-2">No Past Properties</h3>
        <p className="text-gray-600">You haven't rented any properties yet.</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {properties.map((property) => (
        <PastPropertiesCard
          key={property.id}
          property={property}
          onViewDocuments={onViewDocuments}
        />
      ))}
    </div>
  );
};

export default PastPropertiesList;
